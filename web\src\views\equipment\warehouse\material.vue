<template>
    <div class="app-container">
        <!-- 搜索条件区域 -->
        <div class="search-container">
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                label-width="68px">
                <el-form-item label="设备" prop="deviceName">
                    <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable style="width: 200px"
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="物权归属" prop="ownership">
                    <el-select v-model="queryParams.ownership" placeholder="请选择物权归属" clearable style="width: 200px">
                        <el-option label="全部" value="" />
                        <el-option label="医院" value="hospital" />
                        <el-option label="厂家" value="manufacturer" />
                        <el-option label="租赁" value="lease" />
                    </el-select>
                </el-form-item>
                <el-form-item label="设备状态" prop="status">
                    <el-select v-model="queryParams.status" placeholder="请选择设备状态" clearable style="width: 200px">
                        <el-option label="全部" value="" />
                        <el-option label="正常" value="normal" />
                        <el-option label="维修" value="repair" />
                        <el-option label="停用" value="disabled" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-checkbox v-model="queryParams.onlyMyEquipment" label="只显示我的设备" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                    <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 操作按钮区域 -->
        <div class="toolbar-container">
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增设备</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
                        @click="handleUpdate">主要信息修改</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="warning" plain icon="el-icon-document" size="mini" :disabled="single"
                        @click="handleBasicData">基础数据录入</el-button>
                </el-col>
                <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
        </div>

        <!-- 数据表格 -->
        <el-table ref="multipleTable" v-loading="loading" :data="equipmentList"
            @selection-change="handleSelectionChange" @row-click="handleRowClick" stripe border style="width: 100%">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="设备编码" align="center" prop="deviceCode" width="120" />
            <el-table-column label="设备名称" align="left" prop="deviceName" min-width="150" show-overflow-tooltip />
            <el-table-column label="设备型号" align="center" prop="deviceModel" width="120" show-overflow-tooltip />
            <el-table-column label="设备配置" align="center" prop="deviceConfig" width="100" show-overflow-tooltip />
            <el-table-column label="主要用途" align="center" prop="mainUsage" width="120" show-overflow-tooltip />
            <el-table-column label="生产厂家" align="center" prop="manufacturer" width="150" show-overflow-tooltip />
            <el-table-column label="操作" align="center" width="80" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script>
import RightToolbar from "@/components/RightToolbar";
import Pagination from "@/components/Pagination";

export default {
    name: "EquipmentList",
    components: {
        RightToolbar,
        Pagination
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 设备表格数据
            equipmentList: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                deviceName: null,
                ownership: null,
                status: null,
                onlyMyEquipment: false
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询设备列表 */
        getList() {
            this.loading = true;
            // 模拟数据
            setTimeout(() => {
                let allData = [
                    {
                        id: 1,
                        deviceCode: "D20110001",
                        deviceName: "全自动生化分析仪",
                        deviceModel: "AU5800",
                        deviceConfig: "标准配置",
                        mainUsage: "生化检验",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 2,
                        deviceCode: "D20110002",
                        deviceName: "全自动血细胞分析仪",
                        deviceModel: "XN-3000",
                        deviceConfig: "标准配置",
                        mainUsage: "血常规检验",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 3,
                        deviceCode: "D20110003",
                        deviceName: "数字化X光机",
                        deviceModel: "DR-7500",
                        deviceConfig: "数字化",
                        mainUsage: "放射检查",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 4,
                        deviceCode: "D20110004",
                        deviceName: "彩超仪",
                        deviceModel: "LOGIQ E9",
                        deviceConfig: "彩色多普勒",
                        mainUsage: "超声检查",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 5,
                        deviceCode: "D20110005",
                        deviceName: "心电图机",
                        deviceModel: "MAC 5500",
                        deviceConfig: "12导联",
                        mainUsage: "心电检查",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 6,
                        deviceCode: "D20110006",
                        deviceName: "呼吸机",
                        deviceModel: "PB980",
                        deviceConfig: "有创无创",
                        mainUsage: "呼吸支持",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 7,
                        deviceCode: "D20110007",
                        deviceName: "监护仪",
                        deviceModel: "IntelliVue MX800",
                        deviceConfig: "多参数",
                        mainUsage: "生命体征监护",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 8,
                        deviceCode: "D20110008",
                        deviceName: "除颤仪",
                        deviceModel: "LIFEPAK 15",
                        deviceConfig: "双相波",
                        mainUsage: "心脏复苏",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 9,
                        deviceCode: "D20110009",
                        deviceName: "麻醉机",
                        deviceModel: "FLOW-i",
                        deviceConfig: "电子流量计",
                        mainUsage: "麻醉给药",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 10,
                        deviceCode: "D20110010",
                        deviceName: "手术床",
                        deviceModel: "ALPHAMAXX",
                        deviceConfig: "电动多功能",
                        mainUsage: "手术支撑",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 11,
                        deviceCode: "D20110011",
                        deviceName: "输液泵",
                        deviceModel: "Alaris GP",
                        deviceConfig: "容积式",
                        mainUsage: "精确输液",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 12,
                        deviceCode: "D20110012",
                        deviceName: "注射泵",
                        deviceModel: "Alaris CC",
                        deviceConfig: "注射器式",
                        mainUsage: "精确给药",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 13,
                        deviceCode: "D20110013",
                        deviceName: "血液透析机",
                        deviceModel: "4008S",
                        deviceConfig: "在线血液透析滤过",
                        mainUsage: "血液净化",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 14,
                        deviceCode: "D20110014",
                        deviceName: "高频电刀",
                        deviceModel: "VIO 300D",
                        deviceConfig: "双极单极",
                        mainUsage: "电外科",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 15,
                        deviceCode: "D20110015",
                        deviceName: "化学发光仪",
                        deviceModel: "ARCHITECT i2000SR",
                        deviceConfig: "全自动",
                        mainUsage: "免疫检验",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 16,
                        deviceCode: "D20110016",
                        deviceName: "血气分析仪",
                        deviceModel: "ABL90 FLEX",
                        deviceConfig: "便携式",
                        mainUsage: "血气分析",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 17,
                        deviceCode: "D20110017",
                        deviceName: "洗胃机",
                        deviceModel: "DXW-2A",
                        deviceConfig: "电动洗胃",
                        mainUsage: "胃肠清洗",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 18,
                        deviceCode: "D20110018",
                        deviceName: "化学分析仪",
                        deviceModel: "AU5800",
                        deviceConfig: "全自动",
                        mainUsage: "生化检验",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 19,
                        deviceCode: "D20110019",
                        deviceName: "化学发光仪",
                        deviceModel: "ARCHITECT i1000SR",
                        deviceConfig: "全自动",
                        mainUsage: "免疫检验",
                        manufacturer: "河南省医疗器械有限公司"
                    },
                    {
                        id: 20,
                        deviceCode: "D20110020",
                        deviceName: "化学分析仪",
                        deviceModel: "AU680",
                        deviceConfig: "全自动",
                        mainUsage: "生化检验",
                        manufacturer: "河南省医疗器械有限公司"
                    }
                ];

                // 应用搜索过滤
                let filteredData = allData;
                if (this.queryParams.deviceName) {
                    filteredData = filteredData.filter(item =>
                        item.deviceName.includes(this.queryParams.deviceName)
                    );
                }

                // 分页处理
                const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
                const end = start + this.queryParams.pageSize;
                this.equipmentList = filteredData.slice(start, end);
                this.total = filteredData.length;
                this.loading = false;
            }, 500);
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        /** 行点击事件 */
        handleRowClick(row) {
            this.$refs.multipleTable.toggleRowSelection(row);
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.$message.info("新增设备功能");
        },
        /** 修改按钮操作 */
        handleUpdate() {
            this.$message.info("主要信息修改功能");
        },
        /** 基础数据录入 */
        handleBasicData() {
            this.$message.info("基础数据录入功能");
        },
        /** 查看按钮操作 */
        handleView(row) {
            this.$message.info(`查看设备：${row.deviceName}`);
        }
    }
};
</script>

<style lang="scss" scoped>
.search-container {
    background: #fff;
    padding: 20px;
    margin-bottom: 10px;
    border-radius: 4px;
}

.toolbar-container {
    background: #fff;
    padding: 10px 20px;
    margin-bottom: 10px;
    border-radius: 4px;
}

.app-container {
    padding: 20px;
}
</style>
